package com.endovas.cps.entity.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 14:15
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_dicom")
@TableName("t_dicom")
public class Dicom extends MysqlBase {
    public static final String COL_MED_AGENT_ID = "med_agent_id";
    public static final String COL_NAME = "name";
    public static final String COL_SURGERY_STAGE = "surgery_stage";
    public static final String COL_CREATOR_BELONG = "creator_belong";
    public static final String COL_REMARK = "remark";
    public static final String COL_SURGERY_TYPE_ID = "surgery_type_id";
    public static final String COL_PATIENT_NAME = "patient_name";
    public static final String COL_PATIENT_ID = "patient_id";
    public static final String COL_HAS_DICOM_FILE = "has_dicom_file";
    public static final String COL_HOSPITAL_ID = "hospital_id";

    private String name; // 影像文件名称
    private String status; // 状态

    private Boolean hasDicomFile; //是否包含dicom文件

    private String surgeryTypeId;//手术类型
    private String patientId; //关联patient
    private String hospitalId;//医院Id
    private String remark;
    private String surgeryStage; //手术阶段
    private String password; //密码

    private String manufacture;
    private String patientName;

    private String patientNo; //患者dicom编号
    private String patientAge;
    private String patientGender;
    private String modality; //影像类型
    private String contentDate; // 文件内容日期
    private String rowsColumns; // 行列
    private String pixelSpacing; // 像素间距
    private Long fileCount; //文件总数

    private String viewJsonUrl; // dicom预览url
    private String tempFileStatus; // 缓存文件状态（用于重新加载缓存时的）

    //上传人归属
    private String creatorBelong;

    //字节
    private Long fileSize;
    private String medAgentId;

    


}
